import { NextRequest, NextResponse } from 'next/server';
import { isDemoMode } from '@/app/config/demoMode';
import mockBrokerAPI from '@/mocks/endpoints/brokers';

// Get list of available brokers
export async function GET(request: NextRequest) {
  try {
    if (isDemoMode()) {
      // Use mock data in demo mode
      const brokers = await mockBrokerAPI.getBrokers();
      return NextResponse.json({
        success: true,
        brokers,
        demoMode: true
      });
    }

    // In production mode, return real broker list
    const brokers = [
      {
        id: 'kite',
        name: '<PERSON><PERSON> (Zerodha)',
        logo: '🪁',
        description: 'India\'s largest discount broker with advanced trading platform',
        status: 'available',
        features: [
          'Zero brokerage on equity delivery',
          'Advanced charting tools',
          'API access for algorithmic trading',
          'Mobile & web platform'
        ],
        authUrl: `https://kite.zerodha.com/connect/login?v=3&api_key=${process.env.NEXT_PUBLIC_ZERODHA_API_KEY}`,
        demoMode: false
      },
      {
        id: 'grow',
        name: '<PERSON><PERSON>',
        logo: '🌱',
        description: 'Modern investment platform with smart portfolio management',
        status: 'coming_soon',
        features: [
          'Smart portfolio recommendations',
          'Goal-based investing',
          'Automated rebalancing',
          'Low-cost mutual funds'
        ],
        demoMode: false
      },
      {
        id: 'upstox',
        name: 'Upstox',
        logo: '📈',
        description: 'Technology-driven discount broker with powerful trading tools',
        status: 'coming_soon',
        features: [
          'Ultra-low brokerage',
          'Real-time market data',
          'Advanced order types',
          'Research & analytics'
        ],
        demoMode: false
      }
    ];

    return NextResponse.json({
      success: true,
      brokers,
      demoMode: false
    });

  } catch (error) {
    console.error('Error fetching brokers:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch brokers',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
