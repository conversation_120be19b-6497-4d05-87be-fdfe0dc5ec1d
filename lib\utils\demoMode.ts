/**
 * Demo Mode Utility Functions
 * Centralized logic for checking and handling demo mode across the application
 */

/**
 * Check if the application is running in demo mode
 * @returns {boolean} True if demo mode is enabled
 */
export function isDemoMode(): boolean {
  // Check environment variable
  const demoMode = process.env.DEMO_MODE;
  
  // Return true if DEMO_MODE is explicitly set to "true" (case insensitive)
  return demoMode?.toLowerCase() === 'true';
}

/**
 * Get demo mode configuration
 * @returns {object} Demo mode configuration object
 */
export function getDemoModeConfig() {
  return {
    enabled: isDemoMode(),
    showIndicators: isDemoMode(),
    simulateApiDelay: isDemoMode(),
    allowMockAuth: isDemoMode(),
  };
}

/**
 * Add artificial delay to simulate API calls in demo mode
 * @param {number} ms - Milliseconds to delay (default: 500-1500ms random)
 */
export async function simulateApiDelay(ms?: number): Promise<void> {
  if (!isDemoMode()) return;
  
  const delay = ms || Math.floor(Math.random() * 1000) + 500; // 500-1500ms random delay
  await new Promise(resolve => setTimeout(resolve, delay));
}

/**
 * Log demo mode actions for debugging
 * @param {string} action - Action being performed
 * @param {any} data - Optional data to log
 */
export function logDemoAction(action: string, data?: any): void {
  if (!isDemoMode()) return;
  
  console.log(`[DEMO MODE] ${action}`, data ? data : '');
}

/**
 * Get demo mode indicator text for UI
 * @returns {string} Demo mode indicator text
 */
export function getDemoModeIndicator(): string {
  return isDemoMode() ? '🔄 Demo Mode' : '';
}

/**
 * Wrap API responses with demo mode indicators
 * @param {any} data - API response data
 * @returns {any} Data with demo mode indicators if applicable
 */
export function wrapWithDemoIndicator<T>(data: T): T & { isDemoMode?: boolean } {
  if (!isDemoMode()) return data;
  
  return {
    ...data,
    isDemoMode: true
  };
}
