import { NextRequest, NextResponse } from 'next/server'
import { SubscriptionService } from '@/lib/services/subscriptionService'

// Get all available subscription plans
export async function GET(request: NextRequest) {
  try {
    const plans = await SubscriptionService.getAvailablePlans()

    return NextResponse.json({
      success: true,
      plans: plans.map(plan => ({
        id: plan.id,
        name: plan.name,
        type: plan.type,
        description: plan.description,
        price: plan.price,
        currency: plan.currency,
        features: plan.features,
        maxChildren: plan.maxChildren,
        maxTrades: plan.maxTrades,
        isActive: plan.isActive,
      }))
    })

  } catch (error) {
    console.error('Get plans error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get plans', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}
