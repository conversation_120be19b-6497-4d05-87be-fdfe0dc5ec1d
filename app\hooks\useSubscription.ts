import { useState, useEffect } from 'react'
import { useAuth } from '@/app/context/AuthContext'

interface SubscriptionPlan {
  id: string
  name: string
  type: string
  description?: string
  price: number
  currency: string
  features: string[]
  maxChildren: number
  maxTrades: number
  isActive: boolean
}

interface UserSubscription {
  id: string
  status: string
  currentPeriodStart: string
  currentPeriodEnd: string
  trialEnd?: string
  cancelAtPeriodEnd: boolean
  plan: SubscriptionPlan
  limits: {
    canAddChild: boolean
    canPlaceTrade: boolean
    isActive: boolean
  }
}

export function useSubscription() {
  const { user, isAuthenticated } = useAuth()
  const [subscription, setSubscription] = useState<UserSubscription | null>(null)
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch user subscription
  const fetchSubscription = async () => {
    if (!isAuthenticated) return

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/subscriptions')
      if (!response.ok) {
        throw new Error('Failed to fetch subscription')
      }

      const data = await response.json()
      setSubscription(data.subscription)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      console.error('Subscription fetch error:', err)
    } finally {
      setLoading(false)
    }
  }

  // Fetch available plans
  const fetchPlans = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/plans')
      if (!response.ok) {
        throw new Error('Failed to fetch plans')
      }

      const data = await response.json()
      setPlans(data.plans)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      console.error('Plans fetch error:', err)
    } finally {
      setLoading(false)
    }
  }

  // Update subscription plan
  const updatePlan = async (planId: string) => {
    if (!isAuthenticated) return false

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/subscriptions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId }),
      })

      if (!response.ok) {
        throw new Error('Failed to update subscription')
      }

      const data = await response.json()
      
      // Refresh subscription data
      await fetchSubscription()
      
      return true
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      console.error('Subscription update error:', err)
      return false
    } finally {
      setLoading(false)
    }
  }

  // Cancel subscription
  const cancelSubscription = async () => {
    if (!isAuthenticated) return false

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/subscriptions', {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to cancel subscription')
      }

      // Refresh subscription data
      await fetchSubscription()
      
      return true
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      console.error('Subscription cancel error:', err)
      return false
    } finally {
      setLoading(false)
    }
  }

  // Check if user can add more children
  const canAddChild = () => {
    if (!subscription) return false
    return subscription.limits.canAddChild
  }

  // Check if user can place more trades
  const canPlaceTrade = () => {
    if (!subscription) return false
    return subscription.limits.canPlaceTrade
  }

  // Check if subscription is active
  const isActive = () => {
    if (!subscription) return false
    return subscription.limits.isActive
  }

  // Get current plan
  const getCurrentPlan = () => {
    return subscription?.plan || null
  }

  // Check if user is on trial
  const isOnTrial = () => {
    if (!subscription) return false
    return subscription.status === 'TRIALING' && subscription.trialEnd
  }

  // Get trial end date
  const getTrialEndDate = () => {
    if (!subscription || !subscription.trialEnd) return null
    return new Date(subscription.trialEnd)
  }

  // Get subscription end date
  const getSubscriptionEndDate = () => {
    if (!subscription) return null
    return new Date(subscription.currentPeriodEnd)
  }

  // Check if subscription will be cancelled at period end
  const willCancelAtPeriodEnd = () => {
    return subscription?.cancelAtPeriodEnd || false
  }

  // Get remaining trial days
  const getRemainingTrialDays = () => {
    const trialEnd = getTrialEndDate()
    if (!trialEnd) return 0
    
    const now = new Date()
    const diffTime = trialEnd.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(0, diffDays)
  }

  // Load data on mount and when user changes
  useEffect(() => {
    if (isAuthenticated) {
      fetchSubscription()
      fetchPlans()
    }
  }, [isAuthenticated, user?.id])

  return {
    subscription,
    plans,
    loading,
    error,
    fetchSubscription,
    fetchPlans,
    updatePlan,
    cancelSubscription,
    canAddChild,
    canPlaceTrade,
    isActive,
    getCurrentPlan,
    isOnTrial,
    getTrialEndDate,
    getSubscriptionEndDate,
    willCancelAtPeriodEnd,
    getRemainingTrialDays,
  }
}
