// Mock Portfolio Data for Demo Mode
export interface MockHolding {
  symbol: string;
  exchange: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercent: number;
  dayChange: number;
  dayChangePercent: number;
  marketValue: number;
  investedValue: number;
}

export interface MockPortfolioData {
  totalValue: number;
  totalPnL: number;
  totalPnLPercent: number;
  totalDayChange: number;
  totalDayChangePercent: number;
  totalInvestedValue: number;
  holdings: MockHolding[];
  lastUpdated: string;
}

// Generate realistic mock portfolio data
export const generateMockPortfolioData = (): MockPortfolioData => {
  const holdings: MockHolding[] = [
    {
      symbol: "RELIANCE",
      exchange: "NSE",
      quantity: 10,
      averagePrice: 2450.50,
      currentPrice: 2485.75,
      pnl: 352.50,
      pnlPercent: 1.44,
      dayChange: 15.25,
      dayChangePercent: 0.62,
      marketValue: 24857.50,
      investedValue: 24505.00
    },
    {
      symbol: "TCS",
      exchange: "NSE",
      quantity: 5,
      averagePrice: 3650.75,
      currentPrice: 3698.20,
      pnl: 237.25,
      pnlPercent: 1.30,
      dayChange: -12.80,
      dayChangePercent: -0.35,
      marketValue: 18491.00,
      investedValue: 18253.75
    },
    {
      symbol: "INFY",
      exchange: "NSE",
      quantity: 8,
      averagePrice: 1580.25,
      currentPrice: 1595.40,
      pnl: 121.20,
      pnlPercent: 0.96,
      dayChange: 8.15,
      dayChangePercent: 0.51,
      marketValue: 12763.20,
      investedValue: 12642.00
    },
    {
      symbol: "HDFC",
      exchange: "NSE",
      quantity: 12,
      averagePrice: 1650.80,
      currentPrice: 1672.35,
      pnl: 258.60,
      pnlPercent: 1.31,
      dayChange: 5.45,
      dayChangePercent: 0.33,
      marketValue: 20068.20,
      investedValue: 19809.60
    },
    {
      symbol: "ICICIBANK",
      exchange: "NSE",
      quantity: 15,
      averagePrice: 1125.40,
      currentPrice: 1138.75,
      pnl: 200.25,
      pnlPercent: 1.19,
      dayChange: -3.25,
      dayChangePercent: -0.28,
      marketValue: 17081.25,
      investedValue: 16881.00
    },
    {
      symbol: "WIPRO",
      exchange: "NSE",
      quantity: 20,
      averagePrice: 425.60,
      currentPrice: 431.85,
      pnl: 125.00,
      pnlPercent: 1.47,
      dayChange: 2.15,
      dayChangePercent: 0.50,
      marketValue: 8637.00,
      investedValue: 8512.00
    },
    {
      symbol: "BAJFINANCE",
      exchange: "NSE",
      quantity: 3,
      averagePrice: 6850.25,
      currentPrice: 6925.80,
      pnl: 226.65,
      pnlPercent: 1.10,
      dayChange: 45.30,
      dayChangePercent: 0.66,
      marketValue: 20777.40,
      investedValue: 20550.75
    },
    {
      symbol: "HCLTECH",
      exchange: "NSE",
      quantity: 18,
      averagePrice: 1245.30,
      currentPrice: 1258.95,
      pnl: 245.70,
      pnlPercent: 1.10,
      dayChange: 7.85,
      dayChangePercent: 0.63,
      marketValue: 22661.10,
      investedValue: 22415.40
    }
  ];

  // Calculate totals
  const totalInvestedValue = holdings.reduce((sum, holding) => sum + holding.investedValue, 0);
  const totalMarketValue = holdings.reduce((sum, holding) => sum + holding.marketValue, 0);
  const totalPnL = holdings.reduce((sum, holding) => sum + holding.pnl, 0);
  const totalDayChange = holdings.reduce((sum, holding) => sum + (holding.dayChange * holding.quantity), 0);

  return {
    totalValue: totalMarketValue,
    totalPnL: totalPnL,
    totalPnLPercent: (totalPnL / totalInvestedValue) * 100,
    totalDayChange: totalDayChange,
    totalDayChangePercent: (totalDayChange / totalMarketValue) * 100,
    totalInvestedValue: totalInvestedValue,
    holdings: holdings,
    lastUpdated: new Date().toISOString()
  };
};

// Static mock data for consistent testing
export const mockPortfolioData: MockPortfolioData = generateMockPortfolioData();
