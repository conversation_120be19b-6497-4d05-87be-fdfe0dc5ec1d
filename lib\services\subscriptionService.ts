import { PrismaClient, PlanType, SubscriptionStatus } from '@prisma/client'

const prisma = new PrismaClient()

export class SubscriptionService {
  // Get or create default free plan
  static async getFreePlan() {
    let freePlan = await prisma.plan.findFirst({
      where: { type: PlanType.FREE }
    })

    if (!freePlan) {
      freePlan = await prisma.plan.create({
        data: {
          name: 'Free Plan',
          type: PlanType.FREE,
          description: 'Basic features for getting started',
          price: 0,
          features: [
            'Up to 2 child users',
            'Basic trade copying',
            'Email notifications',
            'Demo mode'
          ],
          maxChildren: 2,
          maxTrades: 100, // 100 trades per month
        }
      })
    }

    return freePlan
  }

  // Create subscription for new user
  static async createFreeSubscription(userId: string) {
    const freePlan = await this.getFreePlan()
    
    const now = new Date()
    const trialEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days trial
    const currentPeriodEnd = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000) // 1 year

    return prisma.subscription.create({
      data: {
        userId,
        planId: freePlan.id,
        status: SubscriptionStatus.TRIALING,
        currentPeriodStart: now,
        currentPeriodEnd,
        trialEnd,
      },
      include: {
        plan: true
      }
    })
  }

  // Get user subscription with plan details
  static async getUserSubscription(userId: string) {
    return prisma.subscription.findUnique({
      where: { userId },
      include: {
        plan: true
      }
    })
  }

  // Check if user can add more children
  static async canAddChildUser(userId: string): Promise<boolean> {
    const subscription = await this.getUserSubscription(userId)
    if (!subscription) return false

    const currentChildCount = await prisma.masterChildRelationship.count({
      where: { masterId: userId }
    })

    return currentChildCount < subscription.plan.maxChildren
  }

  // Check if user can place more trades this month
  static async canPlaceTrade(userId: string): Promise<boolean> {
    const subscription = await this.getUserSubscription(userId)
    if (!subscription) return false

    // If unlimited trades (0), always allow
    if (subscription.plan.maxTrades === 0) return true

    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)

    const tradesThisMonth = await prisma.trade.count({
      where: {
        userId,
        createdAt: {
          gte: startOfMonth
        }
      }
    })

    return tradesThisMonth < subscription.plan.maxTrades
  }

  // Get all available plans
  static async getAvailablePlans() {
    return prisma.plan.findMany({
      where: { isActive: true },
      orderBy: { price: 'asc' }
    })
  }

  // Update subscription plan
  static async updateSubscriptionPlan(userId: string, planId: string) {
    return prisma.subscription.update({
      where: { userId },
      data: {
        planId,
        status: SubscriptionStatus.ACTIVE,
        updatedAt: new Date()
      },
      include: {
        plan: true
      }
    })
  }

  // Cancel subscription
  static async cancelSubscription(userId: string) {
    return prisma.subscription.update({
      where: { userId },
      data: {
        cancelAtPeriodEnd: true,
        updatedAt: new Date()
      }
    })
  }

  // Check if subscription is active
  static async isSubscriptionActive(userId: string): Promise<boolean> {
    const subscription = await this.getUserSubscription(userId)
    if (!subscription) return false

    const now = new Date()
    return (
      subscription.status === SubscriptionStatus.ACTIVE ||
      subscription.status === SubscriptionStatus.TRIALING
    ) && subscription.currentPeriodEnd > now
  }
}
