'use client'

import { useAuth } from '@/app/context/AuthContext'
import { useSubscription } from '@/app/hooks/useSubscription'

export default function DebugAuth() {
  const { user, isAuthenticated, loading } = useAuth()
  const { subscription } = useSubscription()

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Authentication Debug
          </h1>
          <p className="mt-2 text-gray-600">
            Debug information for authentication and user state.
          </p>
        </div>

        {/* Authentication State */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Authentication State
          </h2>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <span className="font-medium">Loading:</span>
              <span className={`px-2 py-1 rounded text-sm ${loading ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                {loading ? 'True' : 'False'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="font-medium">Is Authenticated:</span>
              <span className={`px-2 py-1 rounded text-sm ${isAuthenticated ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {isAuthenticated ? 'True' : 'False'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="font-medium">User Exists:</span>
              <span className={`px-2 py-1 rounded text-sm ${user ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {user ? 'True' : 'False'}
              </span>
            </div>
          </div>
        </div>

        {/* User Information */}
        {user && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              User Information
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">ID</label>
                <p className="mt-1 text-sm text-gray-900 font-mono bg-gray-100 p-2 rounded">{user.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <p className="mt-1 text-sm text-gray-900">{user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Role</label>
                <p className="mt-1 text-sm text-gray-900">
                  <span className={`px-2 py-1 rounded text-sm ${user.role === 'master' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}`}>
                    {user.role}
                  </span>
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <p className="mt-1 text-sm text-gray-900">{user.name || 'Not set'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Avatar URL</label>
                <p className="mt-1 text-sm text-gray-900">{user.avatarUrl || 'Not set'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Is Supabase Auth</label>
                <p className="mt-1 text-sm text-gray-900">
                  <span className={`px-2 py-1 rounded text-sm ${user.isSupabaseAuth ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                    {user.isSupabaseAuth ? 'True' : 'False'}
                  </span>
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Subscription Information */}
        {subscription && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              Subscription Information
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Plan Name</label>
                <p className="mt-1 text-sm text-gray-900">{subscription.plan.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <p className="mt-1 text-sm text-gray-900">
                  <span className={`px-2 py-1 rounded text-sm ${subscription.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                    {subscription.status}
                  </span>
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Max Children</label>
                <p className="mt-1 text-sm text-gray-900">{subscription.plan.maxChildren === 0 ? 'Unlimited' : subscription.plan.maxChildren}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Max Trades</label>
                <p className="mt-1 text-sm text-gray-900">{subscription.plan.maxTrades === 0 ? 'Unlimited' : subscription.plan.maxTrades}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Can Add Child</label>
                <p className="mt-1 text-sm text-gray-900">
                  <span className={`px-2 py-1 rounded text-sm ${subscription.limits.canAddChild ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {subscription.limits.canAddChild ? 'Yes' : 'No'}
                  </span>
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Can Place Trade</label>
                <p className="mt-1 text-sm text-gray-900">
                  <span className={`px-2 py-1 rounded text-sm ${subscription.limits.canPlaceTrade ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {subscription.limits.canPlaceTrade ? 'Yes' : 'No'}
                  </span>
                </p>
              </div>
            </div>
          </div>
        )}

        {/* LocalStorage Debug */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            LocalStorage Debug
          </h2>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700">Stored User</label>
              <pre className="mt-1 text-xs text-gray-900 bg-gray-100 p-3 rounded overflow-auto">
                {typeof window !== 'undefined' ? localStorage.getItem('user') || 'No user in localStorage' : 'Server-side rendering'}
              </pre>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Zerodha Access Token</label>
              <pre className="mt-1 text-xs text-gray-900 bg-gray-100 p-3 rounded overflow-auto">
                {typeof window !== 'undefined' ? localStorage.getItem('zerodha_access_token') || 'No token in localStorage' : 'Server-side rendering'}
              </pre>
            </div>
          </div>
        </div>

        {/* Navigation Debug */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Navigation Debug
          </h2>
          <div className="space-y-3">
            <div>
              <h3 className="font-medium">Expected Navigation Items for Master User:</h3>
              <ul className="mt-2 space-y-1 text-sm text-gray-600">
                <li>• Dashboard (/master/dashboard)</li>
                <li>• Configuration (/master/configuration)</li>
                <li>• Demo Trade (/demo/trading)</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium">Conditions for Configuration Tab to Show:</h3>
              <ul className="mt-2 space-y-1 text-sm text-gray-600">
                <li>• isAuthenticated: <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>{isAuthenticated ? '✓' : '✗'}</span></li>
                <li>• user exists: <span className={user ? 'text-green-600' : 'text-red-600'}>{user ? '✓' : '✗'}</span></li>
                <li>• user.role === 'master': <span className={user?.role === 'master' ? 'text-green-600' : 'text-red-600'}>{user?.role === 'master' ? '✓' : '✗'}</span></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
