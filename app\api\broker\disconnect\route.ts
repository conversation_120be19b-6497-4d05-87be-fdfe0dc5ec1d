import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import mockBrokerAPI from '@/mocks/endpoints/brokers';

// Handle broker disconnection
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (isDemoMode()) {
      // Use mock disconnection in demo mode
      const success = await mockBrokerAPI.disconnectBroker(user.id);
      return NextResponse.json({
        success,
        message: success ? 'Successfully disconnected from broker (Demo Mode)' : 'No broker connection found',
        demoMode: true
      });
    }

    // In production mode, handle real broker disconnection
    // This would typically:
    // 1. Revoke access tokens
    // 2. Update database to mark connection as inactive
    // 3. Clear any cached credentials

    return NextResponse.json({
      success: true,
      message: 'Successfully disconnected from broker',
      demoMode: false
    });

  } catch (error) {
    console.error('Error disconnecting from broker:', error);
    return NextResponse.json(
      { 
        error: 'Failed to disconnect from broker',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
