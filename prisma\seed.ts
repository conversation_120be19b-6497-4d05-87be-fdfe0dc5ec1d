import { PrismaClient, UserRole, PlanType } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create demo master user
  const masterPassword = await bcrypt.hash('password123', 10)
  const masterUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo Master',
      role: UserRole.MASTER,
      password: masterPassword,
      isDemo: true,
    },
  })

  console.log('✅ Created demo master user:', masterUser.email)

  // Create demo child users
  const childPassword = await bcrypt.hash('demo123', 10)

  const childUser1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo Child 1',
      role: UserRole.CHILD,
      password: childPassword,
      isDemo: true,
    },
  })

  const childUser2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo Child 2',
      role: UserRole.CHILD,
      password: childPassword,
      isDemo: true,
    },
  })

  console.log('✅ Created demo child users:', childUser1.email, childUser2.email)

  // Create master-child relationships
  await prisma.masterChildRelationship.upsert({
    where: {
      masterId_childId: {
        masterId: masterUser.id,
        childId: childUser1.id,
      },
    },
    update: {},
    create: {
      masterId: masterUser.id,
      childId: childUser1.id,
    },
  })

  await prisma.masterChildRelationship.upsert({
    where: {
      masterId_childId: {
        masterId: masterUser.id,
        childId: childUser2.id,
      },
    },
    update: {},
    create: {
      masterId: masterUser.id,
      childId: childUser2.id,
    },
  })

  console.log('✅ Created master-child relationships')

  // Create demo Zerodha credentials for master
  await prisma.zerodhaCredentials.upsert({
    where: { userId: masterUser.id },
    update: {},
    create: {
      userId: masterUser.id,
      zerodhaUserId: 'DEMO_MASTER_123',
      apiKey: process.env.NEXT_PUBLIC_ZERODHA_API_KEY || 'demo_api_key',
      accessToken: 'demo_access_token_master',
      refreshToken: 'demo_refresh_token_master',
      isConnected: true,
    },
  })

  // Create demo Zerodha credentials for children
  await prisma.zerodhaCredentials.upsert({
    where: { userId: childUser1.id },
    update: {},
    create: {
      userId: childUser1.id,
      zerodhaUserId: 'DEMO_CHILD1_123',
      accessToken: 'demo_access_token_child1',
      refreshToken: 'demo_refresh_token_child1',
      isConnected: true,
    },
  })

  await prisma.zerodhaCredentials.upsert({
    where: { userId: childUser2.id },
    update: {},
    create: {
      userId: childUser2.id,
      zerodhaUserId: 'DEMO_CHILD2_123',
      accessToken: 'demo_access_token_child2',
      refreshToken: 'demo_refresh_token_child2',
      isConnected: true,
    },
  })

  console.log('✅ Created demo Zerodha credentials')

  // Create demo portfolio holdings
  const demoHoldings = [
    { symbol: 'RELIANCE', exchange: 'NSE', quantity: 10, averagePrice: 2400, currentPrice: 2450.50 },
    { symbol: 'TCS', exchange: 'NSE', quantity: 5, averagePrice: 3600, currentPrice: 3650.75 },
    { symbol: 'INFY', exchange: 'NSE', quantity: 15, averagePrice: 1550, currentPrice: 1580.25 },
    { symbol: 'HDFCBANK', exchange: 'NSE', quantity: 8, averagePrice: 1620, currentPrice: 1650.80 },
  ]

  for (const holding of demoHoldings) {
    const pnl = (holding.currentPrice - holding.averagePrice) * holding.quantity
    const dayChange = holding.currentPrice * 0.01 // 1% day change for demo
    const dayChangePercent = (dayChange / holding.currentPrice) * 100

    // Create for master
    await prisma.portfolio.upsert({
      where: {
        userId_symbol_exchange: {
          userId: masterUser.id,
          symbol: holding.symbol,
          exchange: holding.exchange,
        },
      },
      update: {},
      create: {
        userId: masterUser.id,
        symbol: holding.symbol,
        exchange: holding.exchange,
        quantity: holding.quantity,
        averagePrice: holding.averagePrice,
        currentPrice: holding.currentPrice,
        pnl: pnl,
        dayChange: dayChange,
        dayChangePercent: dayChangePercent,
        isDemo: true,
      },
    })

    // Create for child users with slightly different quantities
    await prisma.portfolio.upsert({
      where: {
        userId_symbol_exchange: {
          userId: childUser1.id,
          symbol: holding.symbol,
          exchange: holding.exchange,
        },
      },
      update: {},
      create: {
        userId: childUser1.id,
        symbol: holding.symbol,
        exchange: holding.exchange,
        quantity: Math.floor(holding.quantity * 0.8), // 80% of master's quantity
        averagePrice: holding.averagePrice,
        currentPrice: holding.currentPrice,
        pnl: pnl * 0.8,
        dayChange: dayChange * 0.8,
        dayChangePercent: dayChangePercent,
        isDemo: true,
      },
    })

    await prisma.portfolio.upsert({
      where: {
        userId_symbol_exchange: {
          userId: childUser2.id,
          symbol: holding.symbol,
          exchange: holding.exchange,
        },
      },
      update: {},
      create: {
        userId: childUser2.id,
        symbol: holding.symbol,
        exchange: holding.exchange,
        quantity: Math.floor(holding.quantity * 1.2), // 120% of master's quantity
        averagePrice: holding.averagePrice,
        currentPrice: holding.currentPrice,
        pnl: pnl * 1.2,
        dayChange: dayChange * 1.2,
        dayChangePercent: dayChangePercent,
        isDemo: true,
      },
    })
  }

  console.log('✅ Created demo portfolio holdings')

  // Create subscription plans
  const freePlan = await prisma.plan.upsert({
    where: { name: 'Free Plan' },
    update: {},
    create: {
      name: 'Free Plan',
      type: PlanType.FREE,
      description: 'Basic features for getting started',
      price: 0,
      features: [
        'Up to 2 child users',
        'Basic trade copying',
        'Email notifications',
        'Demo mode',
        '100 trades per month'
      ],
      maxChildren: 2,
      maxTrades: 100,
    },
  })

  const basicPlan = await prisma.plan.upsert({
    where: { name: 'Basic Plan' },
    update: {},
    create: {
      name: 'Basic Plan',
      type: PlanType.BASIC,
      description: 'Perfect for small teams',
      price: 29.99,
      features: [
        'Up to 5 child users',
        'Advanced trade copying',
        'Real-time notifications',
        'Priority support',
        '500 trades per month'
      ],
      maxChildren: 5,
      maxTrades: 500,
    },
  })

  const premiumPlan = await prisma.plan.upsert({
    where: { name: 'Premium Plan' },
    update: {},
    create: {
      name: 'Premium Plan',
      type: PlanType.PREMIUM,
      description: 'For professional traders',
      price: 99.99,
      features: [
        'Up to 20 child users',
        'Advanced analytics',
        'Custom strategies',
        'API access',
        'Unlimited trades'
      ],
      maxChildren: 20,
      maxTrades: 0, // 0 = unlimited
    },
  })

  const enterprisePlan = await prisma.plan.upsert({
    where: { name: 'Enterprise Plan' },
    update: {},
    create: {
      name: 'Enterprise Plan',
      type: PlanType.ENTERPRISE,
      description: 'For large organizations',
      price: 299.99,
      features: [
        'Unlimited child users',
        'White-label solution',
        'Dedicated support',
        'Custom integrations',
        'Unlimited trades',
        'Advanced reporting'
      ],
      maxChildren: 0, // 0 = unlimited
      maxTrades: 0, // 0 = unlimited
    },
  })

  console.log('✅ Created subscription plans')

  // Create free subscriptions for demo users
  await prisma.subscription.upsert({
    where: { userId: masterUser.id },
    update: {},
    create: {
      userId: masterUser.id,
      planId: freePlan.id,
      status: 'TRIALING',
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      trialEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    },
  })

  await prisma.subscription.upsert({
    where: { userId: childUser1.id },
    update: {},
    create: {
      userId: childUser1.id,
      planId: freePlan.id,
      status: 'TRIALING',
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      trialEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    },
  })

  await prisma.subscription.upsert({
    where: { userId: childUser2.id },
    update: {},
    create: {
      userId: childUser2.id,
      planId: freePlan.id,
      status: 'TRIALING',
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      trialEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    },
  })

  console.log('✅ Created demo user subscriptions')

  console.log('🎉 Database seed completed successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Seed failed:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
