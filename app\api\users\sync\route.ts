import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'
import { UserService } from '@/lib/services/userService'

// Sync Supabase user with Prisma database
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get the current user from Supabase
    const { data: { user: supabaseUser }, error } = await supabase.auth.getUser()
    
    if (error || !supabaseUser) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    // Create or get user in Prisma database
    const user = await UserService.createOrGetSupabaseUser({
      id: supabaseUser.id,
      email: supabaseUser.email!,
      user_metadata: supabaseUser.user_metadata
    })

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        supabaseId: user.supabaseId,
        subscription: user.subscription ? {
          id: user.subscription.id,
          status: user.subscription.status,
          plan: {
            name: user.subscription.plan.name,
            type: user.subscription.plan.type,
            maxChildren: user.subscription.plan.maxChildren,
            maxTrades: user.subscription.plan.maxTrades,
          }
        } : null,
        createdAt: user.createdAt,
      }
    })

  } catch (error) {
    console.error('User sync error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to sync user', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

// Get current user with subscription details
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get the current user from Supabase
    const { data: { user: supabaseUser }, error } = await supabase.auth.getUser()
    
    if (error || !supabaseUser) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    // Find user in Prisma database
    const user = await UserService.findBySupabaseId(supabaseUser.id)
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        supabaseId: user.supabaseId,
        subscription: user.subscription ? {
          id: user.subscription.id,
          status: user.subscription.status,
          currentPeriodEnd: user.subscription.currentPeriodEnd,
          trialEnd: user.subscription.trialEnd,
          plan: {
            name: user.subscription.plan.name,
            type: user.subscription.plan.type,
            price: user.subscription.plan.price,
            maxChildren: user.subscription.plan.maxChildren,
            maxTrades: user.subscription.plan.maxTrades,
            features: user.subscription.plan.features,
          }
        } : null,
        zerodhaCredentials: user.zerodhaCredentials ? {
          isConnected: user.zerodhaCredentials.isConnected,
          zerodhaUserId: user.zerodhaCredentials.zerodhaUserId,
        } : null,
        createdAt: user.createdAt,
      }
    })

  } catch (error) {
    console.error('Get user error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get user', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}
