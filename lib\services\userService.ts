import { prisma } from '@/lib/prisma'
import { UserR<PERSON>, User, ZerodhaCredentials, Subscription, Plan } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { SubscriptionService } from './subscriptionService'

export type UserWithCredentials = User & {
  zerodhaCredentials?: ZerodhaCredentials | null
  subscription?: (Subscription & { plan: Plan }) | null
}

export class UserService {
  // Create a new user
  static async createUser(data: {
    email: string
    name?: string
    password: string
    role: UserRole
    isDemo?: boolean
  }): Promise<UserWithCredentials> {
    const hashedPassword = await bcrypt.hash(data.password, 10)

    return prisma.user.create({
      data: {
        email: data.email,
        name: data.name,
        password: hashedPassword,
        role: data.role,
        isDemo: data.isDemo || false,
      },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Find user by email
  static async findByEmail(email: string): Promise<UserWithCredentials | null> {
    return prisma.user.findUnique({
      where: { email },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Find user by ID
  static async findById(id: string): Promise<UserWithCredentials | null> {
    return prisma.user.findUnique({
      where: { id },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Verify user password
  static async verifyPassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.password)
  }

  // Update user
  static async updateUser(id: string, data: Partial<User>): Promise<UserWithCredentials> {
    return prisma.user.update({
      where: { id },
      data,
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Soft delete user
  static async deleteUser(id: string): Promise<UserWithCredentials> {
    return prisma.user.update({
      where: { id },
      data: {
        deletedAt: new Date(),
        isActive: false,
      },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Get all active users
  static async getActiveUsers(): Promise<UserWithCredentials[]> {
    return prisma.user.findMany({
      where: {
        isActive: true,
        deletedAt: null,
      },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Get users by role
  static async getUsersByRole(role: UserRole): Promise<UserWithCredentials[]> {
    return prisma.user.findMany({
      where: {
        role,
        isActive: true,
        deletedAt: null,
      },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Update or create Zerodha credentials
  static async updateZerodhaCredentials(userId: string, data: {
    zerodhaUserId?: string
    apiKey?: string
    apiSecret?: string
    accessToken?: string
    refreshToken?: string
    tokenExpiry?: Date
    isConnected?: boolean
  }): Promise<ZerodhaCredentials> {
    return prisma.zerodhaCredentials.upsert({
      where: { userId },
      update: {
        ...data,
        lastSyncAt: new Date(),
      },
      create: {
        userId,
        ...data,
        lastSyncAt: new Date(),
      },
    })
  }

  // Get Zerodha credentials
  static async getZerodhaCredentials(userId: string): Promise<ZerodhaCredentials | null> {
    return prisma.zerodhaCredentials.findUnique({
      where: { userId },
    })
  }

  // Disconnect Zerodha
  static async disconnectZerodha(userId: string): Promise<ZerodhaCredentials> {
    return prisma.zerodhaCredentials.update({
      where: { userId },
      data: {
        accessToken: null,
        refreshToken: null,
        tokenExpiry: null,
        isConnected: false,
        lastSyncAt: new Date(),
      },
    })
  }

  // Create or get user from Supabase authentication
  static async createOrGetSupabaseUser(supabaseUser: {
    id: string
    email: string
    user_metadata?: {
      name?: string
      full_name?: string
      role?: UserRole
    }
  }): Promise<UserWithCredentials> {
    // First check if user exists by supabaseId
    let user = await prisma.user.findUnique({
      where: { supabaseId: supabaseUser.id },
      include: {
        zerodhaCredentials: true,
        subscription: {
          include: { plan: true }
        }
      },
    })

    if (user) {
      return user
    }

    // Check if user exists by email (for migration purposes)
    user = await prisma.user.findUnique({
      where: { email: supabaseUser.email },
      include: {
        zerodhaCredentials: true,
        subscription: {
          include: { plan: true }
        }
      },
    })

    if (user) {
      // Update existing user with supabaseId
      return prisma.user.update({
        where: { id: user.id },
        data: { supabaseId: supabaseUser.id },
        include: {
          zerodhaCredentials: true,
          subscription: {
            include: { plan: true }
          }
        },
      })
    }

    // Create new user
    const newUser = await prisma.user.create({
      data: {
        email: supabaseUser.email,
        name: supabaseUser.user_metadata?.name || supabaseUser.user_metadata?.full_name,
        role: supabaseUser.user_metadata?.role || UserRole.MASTER,
        supabaseId: supabaseUser.id,
        password: null, // Supabase handles authentication
      },
      include: {
        zerodhaCredentials: true,
        subscription: {
          include: { plan: true }
        }
      },
    })

    // Create free subscription for new user
    await SubscriptionService.createFreeSubscription(newUser.id)

    // Fetch user with subscription
    return prisma.user.findUnique({
      where: { id: newUser.id },
      include: {
        zerodhaCredentials: true,
        subscription: {
          include: { plan: true }
        }
      },
    }) as Promise<UserWithCredentials>
  }

  // Find user by Supabase ID
  static async findBySupabaseId(supabaseId: string): Promise<UserWithCredentials | null> {
    return prisma.user.findUnique({
      where: { supabaseId },
      include: {
        zerodhaCredentials: true,
        subscription: {
          include: { plan: true }
        }
      },
    })
  }

  // Get user with subscription details
  static async getUserWithSubscription(id: string): Promise<UserWithCredentials | null> {
    return prisma.user.findUnique({
      where: { id },
      include: {
        zerodhaCredentials: true,
        subscription: {
          include: { plan: true }
        }
      },
    })
  }
}
