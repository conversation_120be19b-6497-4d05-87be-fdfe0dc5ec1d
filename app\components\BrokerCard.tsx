'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { GlowingBorder } from '@/app/components/ui/animated-border';
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  Link as LinkIcon,
  Unlink,
  Loader2,
  Star
} from 'lucide-react';

interface BrokerCardProps {
  broker: {
    id: string;
    name: string;
    logo: string;
    description: string;
    status: 'available' | 'coming_soon' | 'maintenance';
    features: string[];
  };
  isConnected: boolean;
  isConnecting: boolean;
  onConnect: (brokerId: string) => void;
  onDisconnect: () => void;
  demoMode?: boolean;
}

export default function BrokerCard({
  broker,
  isConnected,
  isConnecting,
  onConnect,
  onDisconnect,
  demoMode = false
}: BrokerCardProps) {
  const getStatusColor = () => {
    switch (broker.status) {
      case 'available':
        return isConnected ? 'cyan' : 'blue';
      case 'coming_soon':
        return 'purple';
      case 'maintenance':
        return 'pink';
      default:
        return 'blue';
    }
  };

  const getStatusBadge = () => {
    if (isConnected) {
      return (
        <Badge className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-300/50 text-green-700 dark:text-green-400">
          <CheckCircle className="w-3 h-3 mr-1" />
          Connected
        </Badge>
      );
    }

    switch (broker.status) {
      case 'available':
        return (
          <Badge className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border-blue-300/50 text-blue-700 dark:text-blue-400">
            Available
          </Badge>
        );
      case 'coming_soon':
        return (
          <Badge className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-300/50 text-purple-700 dark:text-purple-400">
            <Clock className="w-3 h-3 mr-1" />
            Coming Soon
          </Badge>
        );
      case 'maintenance':
        return (
          <Badge className="bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-300/50 text-amber-700 dark:text-amber-400">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Maintenance
          </Badge>
        );
      default:
        return null;
    }
  };

  const handleAction = () => {
    if (isConnected) {
      onDisconnect();
    } else if (broker.status === 'available') {
      onConnect(broker.id);
    }
  };

  const isActionDisabled = isConnecting || broker.status !== 'available';

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <GlowingBorder glowColor={getStatusColor()}>
        <Card className="border-0 bg-background/50 backdrop-blur-sm h-full">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <motion.div
                  className="text-3xl"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {broker.logo}
                </motion.div>
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
                    {broker.name}
                    {demoMode && (
                      <Badge variant="outline" className="text-xs">
                        Demo
                      </Badge>
                    )}
                  </CardTitle>
                  <CardDescription className="text-sm text-muted-foreground">
                    {broker.description}
                  </CardDescription>
                </div>
              </div>
              {getStatusBadge()}
            </div>
          </CardHeader>
          <CardContent>
            {/* Features List */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-foreground mb-2">Features:</h4>
              <ul className="space-y-1">
                {broker.features.slice(0, 3).map((feature, index) => (
                  <li key={index} className="text-xs text-muted-foreground flex items-center">
                    <Star className="w-3 h-3 mr-2 text-yellow-500" />
                    {feature}
                  </li>
                ))}
                {broker.features.length > 3 && (
                  <li className="text-xs text-muted-foreground">
                    +{broker.features.length - 3} more features
                  </li>
                )}
              </ul>
            </div>

            {/* Action Button */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={handleAction}
                disabled={isActionDisabled}
                className={`w-full transition-all duration-300 ${
                  isConnected
                    ? 'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700'
                    : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
                }`}
                variant={isConnected ? 'destructive' : 'default'}
              >
                {isConnecting ? (
                  <div className="flex items-center">
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Connecting...
                  </div>
                ) : isConnected ? (
                  <div className="flex items-center">
                    <Unlink className="w-4 h-4 mr-2" />
                    Disconnect
                  </div>
                ) : broker.status === 'available' ? (
                  <div className="flex items-center">
                    <LinkIcon className="w-4 h-4 mr-2" />
                    Connect
                  </div>
                ) : (
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    {broker.status === 'coming_soon' ? 'Coming Soon' : 'Unavailable'}
                  </div>
                )}
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </GlowingBorder>
    </motion.div>
  );
}
