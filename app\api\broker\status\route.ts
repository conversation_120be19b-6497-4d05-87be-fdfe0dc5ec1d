import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import mockBrokerAPI from '@/mocks/endpoints/brokers';

// Get current broker connection status
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (isDemoMode()) {
      // Use mock data in demo mode
      const connection = await mockBrokerAPI.getBrokerStatus(user.id);
      return NextResponse.json({
        success: true,
        connection,
        demoMode: true
      });
    }

    // In production mode, check real broker connection from database
    // This would typically query the zerodha_credentials table
    // For now, return null as no real connection exists
    return NextResponse.json({
      success: true,
      connection: null,
      demoMode: false
    });

  } catch (error) {
    console.error('Error fetching broker status:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch broker status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
