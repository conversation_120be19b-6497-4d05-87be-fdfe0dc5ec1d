'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { GlowingBorder } from '@/app/components/ui/animated-border';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  PieChart,
  Activity,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  Wallet,
  Target,
  Clock,
  Users,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { MockPortfolioData } from '@/mocks/endpoints/portfolio';

// Interface for portfolio data
interface PortfolioData extends MockPortfolioData {
  isDemoMode?: boolean;
  recentTrades?: Array<{
    symbol: string;
    type: 'BUY' | 'SELL';
    quantity: number;
    price: number;
    time: string;
  }>;
}

// Default/fallback data
const defaultPortfolioData: PortfolioData = {
  totalValue: 0,
  totalPnL: 0,
  totalPnLPercent: 0,
  totalDayChange: 0,
  totalDayChangePercent: 0,
  totalInvestedValue: 0,
  holdings: [],
  lastUpdated: new Date().toISOString(),
  recentTrades: []
};

export default function DashboardContent() {
  const [portfolioData, setPortfolioData] = useState<PortfolioData>(defaultPortfolioData);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch portfolio data from API
  const fetchPortfolioData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/portfolio', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch portfolio data');
      }

      if (result.success && result.data) {
        // Add mock recent trades for demo purposes
        const portfolioWithTrades: PortfolioData = {
          ...result.data,
          recentTrades: [
            { symbol: 'RELIANCE', type: 'BUY', quantity: 10, price: 2456.75, time: '10:30 AM' },
            { symbol: 'TCS', type: 'SELL', quantity: 5, price: 3567.80, time: '11:15 AM' },
            { symbol: 'INFY', type: 'BUY', quantity: 15, price: 1789.60, time: '2:45 PM' }
          ]
        };
        setPortfolioData(portfolioWithTrades);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching portfolio data:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      // Keep default data on error
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchPortfolioData();
  }, []);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        <div className="flex-grow flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-lg font-medium text-foreground">Loading Portfolio...</p>
            <p className="text-sm text-muted-foreground">Fetching your latest data</p>
          </motion.div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        <div className="flex-grow flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-md mx-auto"
          >
            <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
            <p className="text-lg font-medium text-foreground mb-2">Unable to Load Portfolio</p>
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <button
              onClick={fetchPortfolioData}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Animated background gradient */}
      <div className="fixed inset-0 bg-gradient-to-br from-blue-50/50 via-background to-purple-50/50 dark:from-blue-950/10 dark:via-background dark:to-purple-950/10" />

      {/* Animated gradient overlay */}
      <motion.div
        className="fixed inset-0 opacity-30 dark:opacity-20 pointer-events-none"
        animate={{
          background: [
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 50% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)"
          ]
        }}
        transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
      />

      {/* Floating orbs */}
      <motion.div
        className="fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/30 to-purple-400/30 dark:from-blue-400/20 dark:to-purple-400/20 rounded-full blur-xl pointer-events-none"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
      />
      <motion.div
        className="fixed top-40 right-20 w-24 h-24 bg-gradient-to-r from-purple-400/30 to-pink-400/30 dark:from-purple-400/20 dark:to-pink-400/20 rounded-full blur-xl pointer-events-none"
        animate={{
          y: [0, 20, 0],
          x: [0, -15, 0],
        }}
        transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
      />

      <main className="flex-grow relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            className="flex items-center justify-between mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div>
              <motion.h1
                className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Portfolio Dashboard
              </motion.h1>
              {portfolioData.isDemoMode && (
                <motion.p
                  className="text-sm text-blue-600 font-medium mt-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  🔄 Demo Mode - Simulated Data
                </motion.p>
              )}
            </div>
            <motion.button
              onClick={fetchPortfolioData}
              className="px-4 py-2 bg-blue-600/10 text-blue-600 rounded-lg hover:bg-blue-600/20 transition-colors border border-blue-600/20"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              Refresh
            </motion.button>
          </motion.div>

          {/* Portfolio Overview Cards - Bento Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            {/* Total Portfolio Value */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <GlowingBorder glowColor="blue">
                <Card className="border-0 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                        <p className="text-2xl font-bold text-foreground">
                          ₹{portfolioData.totalValue.toLocaleString()}
                        </p>
                      </div>
                      <div className="p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg">
                        <Wallet className="w-6 h-6 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>

            {/* Total P&L */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <GlowingBorder glowColor={portfolioData.totalPnL >= 0 ? "cyan" : "pink"}>
                <Card className="border-0 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total P&L</p>
                        <p className={`text-2xl font-bold ${portfolioData.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {portfolioData.totalPnL >= 0 ? '+' : ''}₹{portfolioData.totalPnL.toLocaleString()}
                        </p>
                        <p className={`text-sm ${portfolioData.totalPnLPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {portfolioData.totalPnLPercent >= 0 ? '+' : ''}{portfolioData.totalPnLPercent.toFixed(2)}%
                        </p>
                      </div>
                      <div className={`p-3 rounded-lg ${portfolioData.totalPnL >= 0 ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10' : 'bg-gradient-to-r from-red-500/10 to-pink-500/10'}`}>
                        {portfolioData.totalPnL >= 0 ?
                          <TrendingUp className="w-6 h-6 text-green-600" /> :
                          <TrendingDown className="w-6 h-6 text-red-600" />
                        }
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>

            {/* Day Change */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <GlowingBorder glowColor={portfolioData.totalDayChange >= 0 ? "cyan" : "pink"}>
                <Card className="border-0 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Day Change</p>
                        <p className={`text-2xl font-bold ${portfolioData.totalDayChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {portfolioData.totalDayChange >= 0 ? '+' : ''}₹{portfolioData.totalDayChange.toLocaleString()}
                        </p>
                        <p className={`text-sm ${portfolioData.totalDayChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {portfolioData.totalDayChangePercent >= 0 ? '+' : ''}{portfolioData.totalDayChangePercent.toFixed(2)}%
                        </p>
                      </div>
                      <div className={`p-3 rounded-lg ${portfolioData.totalDayChange >= 0 ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10' : 'bg-gradient-to-r from-red-500/10 to-pink-500/10'}`}>
                        {portfolioData.totalDayChange >= 0 ?
                          <ArrowUpRight className="w-6 h-6 text-green-600" /> :
                          <ArrowDownRight className="w-6 h-6 text-red-600" />
                        }
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>

            {/* Active Holdings */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <GlowingBorder glowColor="purple">
                <Card className="border-0 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Holdings</p>
                        <p className="text-2xl font-bold text-foreground">
                          {portfolioData.holdings.length}
                        </p>
                        <p className="text-sm text-muted-foreground">Active stocks</p>
                      </div>
                      <div className="p-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg">
                        <PieChart className="w-6 h-6 text-purple-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>
          </motion.div>

          {/* Holdings Table */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mb-8"
          >
            <GlowingBorder className="h-full" glowColor="blue">
              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <BarChart3 className="w-6 h-6 text-blue-600" />
                    </motion.div>
                    <div>
                      <CardTitle className="text-xl font-semibold text-foreground">Portfolio Holdings</CardTitle>
                      <CardDescription className="text-muted-foreground">
                        Your current stock positions
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {portfolioData.holdings.length > 0 ? (
                      portfolioData.holdings.map((holding, index) => (
                        <motion.div
                          key={holding.symbol}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.1 }}
                          className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-500/5 to-slate-500/5 rounded-lg border border-border/50"
                        >
                          <div className="flex items-center space-x-4">
                            <div className="p-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg">
                              <Target className="w-5 h-5 text-blue-600" />
                            </div>
                            <div>
                              <p className="font-semibold text-foreground">{holding.symbol}</p>
                              <p className="text-sm text-muted-foreground">
                                Qty: {holding.quantity} | Avg: ₹{holding.averagePrice.toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-foreground">₹{holding.currentPrice.toLocaleString()}</p>
                            <p className={`text-sm ${holding.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {holding.pnl >= 0 ? '+' : ''}₹{holding.pnl.toLocaleString()} ({holding.pnlPercent >= 0 ? '+' : ''}{holding.pnlPercent.toFixed(2)}%)
                            </p>
                          </div>
                        </motion.div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground">No holdings found</p>
                        <p className="text-sm text-muted-foreground mt-1">Start trading to see your portfolio here</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </GlowingBorder>
          </motion.div>

          {/* Recent Trades and Market Data Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Recent Trades */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <GlowingBorder className="h-full" glowColor="purple">
                <Card className="border-0 bg-background/50 backdrop-blur-sm h-full">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <motion.div
                        className="p-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Activity className="w-6 h-6 text-purple-600" />
                      </motion.div>
                      <div>
                        <CardTitle className="text-xl font-semibold text-foreground">Recent Trades</CardTitle>
                        <CardDescription className="text-muted-foreground">
                          Latest trading activity
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {portfolioData.recentTrades && portfolioData.recentTrades.length > 0 ? (
                        portfolioData.recentTrades.map((trade, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.4, delay: index * 0.1 }}
                            className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-500/5 to-slate-500/5 rounded-lg border border-border/50"
                          >
                            <div className="flex items-center space-x-3">
                              <div className={`p-2 rounded-lg ${trade.type === 'BUY' ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10' : 'bg-gradient-to-r from-red-500/10 to-pink-500/10'}`}>
                                {trade.type === 'BUY' ?
                                  <ArrowUpRight className="w-4 h-4 text-green-600" /> :
                                  <ArrowDownRight className="w-4 h-4 text-red-600" />
                                }
                              </div>
                              <div>
                                <p className="font-semibold text-foreground">{trade.symbol}</p>
                                <p className="text-sm text-muted-foreground">{trade.type} {trade.quantity}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-semibold text-foreground">₹{trade.price.toLocaleString()}</p>
                              <p className="text-sm text-muted-foreground">{trade.time}</p>
                            </div>
                          </motion.div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <p className="text-muted-foreground">No recent trades</p>
                          <p className="text-sm text-muted-foreground mt-1">Your trading activity will appear here</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>

            {/* Market Data Widget */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
            >
              <GlowingBorder className="h-full" glowColor="cyan">
                <Card className="border-0 bg-background/50 backdrop-blur-sm h-full">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <motion.div
                        className="p-3 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-lg"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <BarChart3 className="w-6 h-6 text-cyan-600" />
                      </motion.div>
                      <div>
                        <CardTitle className="text-xl font-semibold text-foreground">Market Overview</CardTitle>
                        <CardDescription className="text-muted-foreground">
                          Key market indicators
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-500/5 to-slate-500/5 rounded-lg border border-border/50">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg">
                            <TrendingUp className="w-4 h-4 text-green-600" />
                          </div>
                          <div>
                            <p className="font-semibold text-foreground">NIFTY 50</p>
                            <p className="text-sm text-muted-foreground">NSE</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-foreground">19,674.25</p>
                          <p className="text-sm text-green-600">+234.50 (+1.21%)</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-500/5 to-slate-500/5 rounded-lg border border-border/50">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-gradient-to-r from-red-500/10 to-pink-500/10 rounded-lg">
                            <TrendingDown className="w-4 h-4 text-red-600" />
                          </div>
                          <div>
                            <p className="font-semibold text-foreground">SENSEX</p>
                            <p className="text-sm text-muted-foreground">BSE</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-foreground">65,832.10</p>
                          <p className="text-sm text-red-600">-123.45 (-0.19%)</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>
          </div>
        </div>
      </main>
    </div>
  );
}
