'use client'

import { useSubscription } from '@/app/hooks/useSubscription'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Button } from '@/app/components/ui/button'
import { Separator } from '@/app/components/ui/separator'
import { CheckCircle, Clock, Users, TrendingUp, AlertTriangle } from 'lucide-react'

export function SubscriptionCard() {
  const {
    subscription,
    loading,
    error,
    isActive,
    getCurrentPlan,
    isOnTrial,
    getRemainingTrialDays,
    willCancelAtPeriodEnd,
    canAddChild,
    canPlaceTrade,
  } = useSubscription()

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Error loading subscription: {error}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!subscription) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            No subscription found
          </div>
        </CardContent>
      </Card>
    )
  }

  const plan = getCurrentPlan()
  const trialDays = getRemainingTrialDays()

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <span>{plan?.name}</span>
              <Badge variant={isActive() ? 'default' : 'destructive'}>
                {subscription.status}
              </Badge>
            </CardTitle>
            <CardDescription>
              {plan?.description}
            </CardDescription>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">
              ${plan?.price}
              <span className="text-sm font-normal text-gray-500">/month</span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Trial Information */}
        {isOnTrial() && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 text-blue-800">
              <Clock className="h-5 w-5" />
              <span className="font-medium">
                Trial Period: {trialDays} days remaining
              </span>
            </div>
          </div>
        )}

        {/* Cancellation Notice */}
        {willCancelAtPeriodEnd() && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-medium">
                Subscription will cancel at period end
              </span>
            </div>
          </div>
        )}

        {/* Usage Limits */}
        <div className="space-y-4">
          <h4 className="font-medium">Current Usage & Limits</h4>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-3">
              <Users className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium">Child Users</div>
                <div className="text-xs text-gray-500">
                  {plan?.maxChildren === 0 ? 'Unlimited' : `Up to ${plan?.maxChildren}`}
                </div>
                <Badge variant={canAddChild() ? 'default' : 'secondary'} className="mt-1">
                  {canAddChild() ? 'Can add more' : 'Limit reached'}
                </Badge>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <TrendingUp className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium">Monthly Trades</div>
                <div className="text-xs text-gray-500">
                  {plan?.maxTrades === 0 ? 'Unlimited' : `Up to ${plan?.maxTrades}`}
                </div>
                <Badge variant={canPlaceTrade() ? 'default' : 'secondary'} className="mt-1">
                  {canPlaceTrade() ? 'Available' : 'Limit reached'}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Features */}
        <div className="space-y-3">
          <h4 className="font-medium">Plan Features</h4>
          <div className="space-y-2">
            {plan?.features.map((feature, index) => (
              <div key={index} className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">{feature}</span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Actions */}
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            View All Plans
          </Button>
          {plan?.type !== 'ENTERPRISE' && (
            <Button size="sm">
              Upgrade Plan
            </Button>
          )}
          {!willCancelAtPeriodEnd() && (
            <Button variant="destructive" size="sm">
              Cancel Subscription
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
