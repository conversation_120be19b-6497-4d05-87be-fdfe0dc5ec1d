import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'
import { UserService } from '@/lib/services/userService'
import { SubscriptionService } from '@/lib/services/subscriptionService'

// Get user subscription details
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get the current user from Supabase
    const { data: { user: supabaseUser }, error } = await supabase.auth.getUser()
    
    if (error || !supabaseUser) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    // Find user in Prisma database
    const user = await UserService.findBySupabaseId(supabaseUser.id)
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      )
    }

    // Get subscription details
    const subscription = await SubscriptionService.getUserSubscription(user.id)
    
    if (!subscription) {
      return NextResponse.json(
        { error: 'No subscription found' },
        { status: 404 }
      )
    }

    // Check subscription limits
    const canAddChild = await SubscriptionService.canAddChildUser(user.id)
    const canPlaceTrade = await SubscriptionService.canPlaceTrade(user.id)
    const isActive = await SubscriptionService.isSubscriptionActive(user.id)

    return NextResponse.json({
      success: true,
      subscription: {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        trialEnd: subscription.trialEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        plan: {
          id: subscription.plan.id,
          name: subscription.plan.name,
          type: subscription.plan.type,
          price: subscription.plan.price,
          currency: subscription.plan.currency,
          maxChildren: subscription.plan.maxChildren,
          maxTrades: subscription.plan.maxTrades,
          features: subscription.plan.features,
        },
        limits: {
          canAddChild,
          canPlaceTrade,
          isActive,
        }
      }
    })

  } catch (error) {
    console.error('Get subscription error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get subscription', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

// Update subscription plan
export async function PUT(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get the current user from Supabase
    const { data: { user: supabaseUser }, error } = await supabase.auth.getUser()
    
    if (error || !supabaseUser) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    const { planId } = await request.json()

    if (!planId) {
      return NextResponse.json(
        { error: 'Plan ID is required' },
        { status: 400 }
      )
    }

    // Find user in Prisma database
    const user = await UserService.findBySupabaseId(supabaseUser.id)
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      )
    }

    // Update subscription
    const updatedSubscription = await SubscriptionService.updateSubscriptionPlan(user.id, planId)

    return NextResponse.json({
      success: true,
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        plan: {
          name: updatedSubscription.plan.name,
          type: updatedSubscription.plan.type,
          price: updatedSubscription.plan.price,
          maxChildren: updatedSubscription.plan.maxChildren,
          maxTrades: updatedSubscription.plan.maxTrades,
        }
      }
    })

  } catch (error) {
    console.error('Update subscription error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to update subscription', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

// Cancel subscription
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get the current user from Supabase
    const { data: { user: supabaseUser }, error } = await supabase.auth.getUser()
    
    if (error || !supabaseUser) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    // Find user in Prisma database
    const user = await UserService.findBySupabaseId(supabaseUser.id)
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      )
    }

    // Cancel subscription
    const cancelledSubscription = await SubscriptionService.cancelSubscription(user.id)

    return NextResponse.json({
      success: true,
      message: 'Subscription cancelled successfully',
      subscription: {
        id: cancelledSubscription.id,
        cancelAtPeriodEnd: cancelledSubscription.cancelAtPeriodEnd,
        currentPeriodEnd: cancelledSubscription.currentPeriodEnd,
      }
    })

  } catch (error) {
    console.error('Cancel subscription error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to cancel subscription', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}
