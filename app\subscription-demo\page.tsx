'use client'

import { SubscriptionCard } from '@/app/components/subscription/SubscriptionCard'
import { useAuth } from '@/app/context/AuthContext'
import { useSubscription } from '@/app/hooks/useSubscription'

export default function SubscriptionDemo() {
  const { user, isAuthenticated } = useAuth()
  const { plans, loading: plansLoading } = useSubscription()

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Please log in to view subscription details
          </h1>
          <p className="text-gray-600">
            You need to be authenticated to access subscription information.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Subscription Management
          </h1>
          <p className="mt-2 text-gray-600">
            Manage your CopyTrade subscription and view available plans.
          </p>
        </div>

        {/* User Info */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Account Information
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <p className="mt-1 text-sm text-gray-900">{user?.email}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Role
              </label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{user?.role}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Name
              </label>
              <p className="mt-1 text-sm text-gray-900">{user?.name || 'Not set'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                User ID
              </label>
              <p className="mt-1 text-sm text-gray-900 font-mono">{user?.id}</p>
            </div>
          </div>
        </div>

        {/* Current Subscription */}
        <div className="mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Current Subscription
          </h2>
          <SubscriptionCard />
        </div>

        {/* Available Plans */}
        <div className="mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Available Plans
          </h2>
          
          {plansLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg shadow p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                      <div className="h-3 bg-gray-200 rounded w-4/6"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {plans.map((plan) => (
                <div key={plan.id} className="bg-white rounded-lg shadow p-6">
                  <div className="mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      {plan.name}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">
                      {plan.description}
                    </p>
                  </div>
                  
                  <div className="mb-4">
                    <span className="text-3xl font-bold text-gray-900">
                      ${plan.price}
                    </span>
                    <span className="text-gray-600">/month</span>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">
                      Features:
                    </h4>
                    <ul className="space-y-1">
                      {plan.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-center">
                          <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></span>
                          {feature}
                        </li>
                      ))}
                      {plan.features.length > 3 && (
                        <li className="text-sm text-gray-500">
                          +{plan.features.length - 3} more features
                        </li>
                      )}
                    </ul>
                  </div>

                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>Max Children:</span>
                      <span>{plan.maxChildren === 0 ? 'Unlimited' : plan.maxChildren}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Trades:</span>
                      <span>{plan.maxTrades === 0 ? 'Unlimited' : plan.maxTrades}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* API Endpoints Info */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Available API Endpoints
          </h2>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">
                GET
              </span>
              <span className="font-mono">/api/users/sync</span>
              <span className="text-gray-600">- Sync user with database</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">
                POST
              </span>
              <span className="font-mono">/api/users/sync</span>
              <span className="text-gray-600">- Create/update user</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">
                GET
              </span>
              <span className="font-mono">/api/subscriptions</span>
              <span className="text-gray-600">- Get user subscription</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-mono">
                PUT
              </span>
              <span className="font-mono">/api/subscriptions</span>
              <span className="text-gray-600">- Update subscription plan</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-mono">
                DEL
              </span>
              <span className="font-mono">/api/subscriptions</span>
              <span className="text-gray-600">- Cancel subscription</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">
                GET
              </span>
              <span className="font-mono">/api/plans</span>
              <span className="text-gray-600">- Get available plans</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
