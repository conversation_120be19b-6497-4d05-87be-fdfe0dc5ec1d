// Mock Broker Data for Demo Mode
export interface MockBroker {
  id: string;
  name: string;
  logo: string;
  description: string;
  status: 'available' | 'coming_soon' | 'maintenance';
  features: string[];
  authUrl?: string;
  demoMode: boolean;
}

export interface MockBrokerConnection {
  brokerId: string;
  brokerName: string;
  userId: string;
  accountId: string;
  connectedAt: string;
  status: 'active' | 'inactive' | 'error';
  accessToken?: string;
  refreshToken?: string;
}

// Available brokers
export const mockBrokers: MockBroker[] = [
  {
    id: 'kite',
    name: 'Kite (Zerodha)',
    logo: '🪁',
    description: 'India\'s largest discount broker with advanced trading platform',
    status: 'available',
    features: [
      'Zero brokerage on equity delivery',
      'Advanced charting tools',
      'API access for algorithmic trading',
      'Mobile & web platform'
    ],
    authUrl: 'https://kite.zerodha.com/connect/login',
    demoMode: true
  },
  {
    id: 'grow',
    name: '<PERSON><PERSON>',
    logo: '🌱',
    description: 'Modern investment platform with smart portfolio management',
    status: 'available',
    features: [
      'Smart portfolio recommendations',
      'Goal-based investing',
      'Automated rebalancing',
      'Low-cost mutual funds'
    ],
    authUrl: 'https://grow.com/oauth/authorize',
    demoMode: true
  },
  {
    id: 'upstox',
    name: 'Upstox',
    logo: '📈',
    description: 'Technology-driven discount broker with powerful trading tools',
    status: 'coming_soon',
    features: [
      'Ultra-low brokerage',
      'Real-time market data',
      'Advanced order types',
      'Research & analytics'
    ],
    authUrl: 'https://api.upstox.com/v2/login/authorization',
    demoMode: true
  }
];

// Mock broker connections (simulated database)
let mockBrokerConnections: MockBrokerConnection[] = [];

// Generate mock broker connection
export const generateMockBrokerConnection = (
  brokerId: string,
  userId: string
): MockBrokerConnection => {
  const broker = mockBrokers.find(b => b.id === brokerId);
  if (!broker) {
    throw new Error(`Broker ${brokerId} not found`);
  }

  return {
    brokerId,
    brokerName: broker.name,
    userId,
    accountId: `${brokerId.toUpperCase()}_${userId.slice(-6)}`,
    connectedAt: new Date().toISOString(),
    status: 'active',
    accessToken: `mock_access_${brokerId}_${Date.now()}`,
    refreshToken: `mock_refresh_${brokerId}_${Date.now()}`
  };
};

// Mock API functions
export const mockBrokerAPI = {
  // Get all available brokers
  getBrokers: async (): Promise<MockBroker[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockBrokers;
  },

  // Get user's broker connection status
  getBrokerStatus: async (userId: string): Promise<MockBrokerConnection | null> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockBrokerConnections.find(conn => conn.userId === userId) || null;
  },

  // Connect to a broker
  connectBroker: async (brokerId: string, userId: string): Promise<MockBrokerConnection> => {
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const broker = mockBrokers.find(b => b.id === brokerId);
    if (!broker) {
      throw new Error(`Broker ${brokerId} not found`);
    }

    if (broker.status !== 'available') {
      throw new Error(`Broker ${broker.name} is currently ${broker.status}`);
    }

    // Remove any existing connection for this user
    mockBrokerConnections = mockBrokerConnections.filter(conn => conn.userId !== userId);

    // Create new connection
    const connection = generateMockBrokerConnection(brokerId, userId);
    mockBrokerConnections.push(connection);

    return connection;
  },

  // Disconnect from broker
  disconnectBroker: async (userId: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const initialLength = mockBrokerConnections.length;
    mockBrokerConnections = mockBrokerConnections.filter(conn => conn.userId !== userId);
    
    return mockBrokerConnections.length < initialLength;
  },

  // Simulate OAuth callback
  handleOAuthCallback: async (
    brokerId: string,
    authCode: string,
    userId: string
  ): Promise<MockBrokerConnection> => {
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // In real implementation, this would exchange auth code for tokens
    return mockBrokerAPI.connectBroker(brokerId, userId);
  }
};

// Export for use in API routes
export default mockBrokerAPI;
