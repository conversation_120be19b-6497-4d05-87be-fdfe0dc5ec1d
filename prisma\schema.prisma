// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Enums
enum UserRole {
  MASTER
  CHILD
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  CANCELLED
}

enum TradeStatus {
  PENDING
  COMPLETE
  REJECTED
  CANCELLED
}

enum OrderType {
  MARKET
  LIMIT
  SL
  SL_M
}

enum TransactionType {
  BUY
  SELL
}

enum ProductType {
  CNC
  MIS
  NRML
}

enum NotificationType {
  TRADE_COPIED
  INVITATION_SENT
  INVITATION_ACCEPTED
  SYSTEM_ALERT
  ERROR
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  PAST_DUE
  TRIALING
}

enum PlanType {
  FREE
  BASIC
  PREMIUM
  ENTERPRISE
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum AuditAction {
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  TRADE_PLACED
  TRADE_COPIED
  INVITATION_SENT
  INVITATION_ACCEPTED
  LOGIN
  LOGOUT
  ZERODHA_CONNECTED
  ZERODHA_DISCONNECTED
}

// Main Models
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  role      UserRole
  password  String?  // Optional for Supabase users
  isActive  Boolean  @default(true)
  isDemo    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?

  // Supabase Integration
  supabaseId String? @unique // Supabase user ID

  // Subscription
  subscription Subscription?

  // Zerodha Integration
  zerodhaCredentials ZerodhaCredentials?

  // Master-Child Relationships
  masterRelationships MasterChildRelationship[] @relation("MasterUser")
  childRelationships  MasterChildRelationship[] @relation("ChildUser")

  // Invitations
  sentInvitations     Invitation[] @relation("InvitationSender")
  receivedInvitations Invitation[] @relation("InvitationReceiver")

  // Trading
  trades     Trade[]
  orders     Order[]
  portfolios Portfolio[]

  // Notifications
  notifications Notification[]

  // Audit Logs
  auditLogs AuditLog[]

  // Payments
  payments Payment[]

  @@map("users")
}

model ZerodhaCredentials {
  id           String    @id @default(cuid())
  userId       String    @unique
  zerodhaUserId String?
  apiKey       String?
  apiSecret    String?
  accessToken  String?
  refreshToken String?
  tokenExpiry  DateTime?
  isConnected  Boolean   @default(false)
  lastSyncAt   DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("zerodha_credentials")
}

model MasterChildRelationship {
  id          String    @id @default(cuid())
  masterId    String
  childId     String
  isActive    Boolean   @default(true)
  connectedAt DateTime  @default(now())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  master User @relation("MasterUser", fields: [masterId], references: [id], onDelete: Cascade)
  child  User @relation("ChildUser", fields: [childId], references: [id], onDelete: Cascade)

  // Trade copying
  tradeCopies TradeCopy[]

  @@unique([masterId, childId])
  @@map("master_child_relationships")
}

model Invitation {
  id           String           @id @default(cuid())
  senderId     String
  receiverEmail String
  receiverId   String?
  status       InvitationStatus @default(PENDING)
  token        String           @unique
  expiresAt    DateTime
  acceptedAt   DateTime?
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  sender   User  @relation("InvitationSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver User? @relation("InvitationReceiver", fields: [receiverId], references: [id], onDelete: SetNull)

  @@map("invitations")
}

model Trade {
  id              String          @id @default(cuid())
  userId          String
  symbol          String
  exchange        String
  transactionType TransactionType
  quantity        Int
  price           Decimal         @db.Decimal(10, 2)
  orderType       OrderType
  productType     ProductType
  status          TradeStatus     @default(PENDING)
  zerodhaOrderId  String?
  executedAt      DateTime?
  isDemo          Boolean         @default(false)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Trade copying
  originalTradeCopies TradeCopy[] @relation("OriginalTrade")
  copiedTradeCopies   TradeCopy[] @relation("CopiedTrade")

  @@map("trades")
}

model Order {
  id              String          @id @default(cuid())
  userId          String
  symbol          String
  exchange        String
  transactionType TransactionType
  quantity        Int
  price           Decimal?        @db.Decimal(10, 2)
  triggerPrice    Decimal?        @db.Decimal(10, 2)
  orderType       OrderType
  productType     ProductType
  validity        String          @default("DAY")
  status          String
  zerodhaOrderId  String?
  averagePrice    Decimal?        @db.Decimal(10, 2)
  filledQuantity  Int             @default(0)
  pendingQuantity Int
  cancelledQuantity Int           @default(0)
  statusMessage   String?
  orderTimestamp  DateTime?
  exchangeTimestamp DateTime?
  isDemo          Boolean         @default(false)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("orders")
}

model Portfolio {
  id           String  @id @default(cuid())
  userId       String
  symbol       String
  exchange     String
  quantity     Int
  averagePrice Decimal @db.Decimal(10, 2)
  currentPrice Decimal @db.Decimal(10, 2)
  pnl          Decimal @db.Decimal(10, 2)
  dayChange    Decimal @db.Decimal(10, 2) @default(0)
  dayChangePercent Decimal @db.Decimal(5, 2) @default(0)
  isDemo       Boolean @default(false)
  lastUpdated  DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, symbol, exchange])
  @@map("portfolios")
}

model TradeCopy {
  id             String   @id @default(cuid())
  relationshipId String
  originalTradeId String
  copiedTradeId  String
  copiedAt       DateTime @default(now())
  status         String   @default("SUCCESS")
  errorMessage   String?

  relationship  MasterChildRelationship @relation(fields: [relationshipId], references: [id], onDelete: Cascade)
  originalTrade Trade                   @relation("OriginalTrade", fields: [originalTradeId], references: [id], onDelete: Cascade)
  copiedTrade   Trade                   @relation("CopiedTrade", fields: [copiedTradeId], references: [id], onDelete: Cascade)

  @@unique([originalTradeId, copiedTradeId])
  @@map("trade_copies")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  data      Json?
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model AuditLog {
  id          String      @id @default(cuid())
  userId      String?
  action      AuditAction
  description String
  metadata    Json?
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime    @default(now())

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
}

// Subscription Models
model Plan {
  id          String   @id @default(cuid())
  name        String   @unique
  type        PlanType
  description String?
  price       Decimal  @db.Decimal(10, 2)
  currency    String   @default("USD")
  features    Json     // Array of features
  maxChildren Int      @default(0) // Max child users allowed
  maxTrades   Int      @default(0) // Max trades per month (0 = unlimited)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  subscriptions Subscription[]

  @@map("plans")
}

model Subscription {
  id                String             @id @default(cuid())
  userId            String             @unique
  planId            String
  status            SubscriptionStatus @default(TRIALING)
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  cancelAtPeriodEnd Boolean            @default(false)
  trialEnd          DateTime?
  stripeCustomerId  String?            @unique
  stripeSubscriptionId String?         @unique
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan Plan @relation(fields: [planId], references: [id])

  @@map("subscriptions")
}

model Payment {
  id               String        @id @default(cuid())
  userId           String
  subscriptionId   String?
  amount           Decimal       @db.Decimal(10, 2)
  currency         String        @default("USD")
  status           PaymentStatus @default(PENDING)
  stripePaymentId  String?       @unique
  paymentMethod    String?
  description      String?
  paidAt           DateTime?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("payments")
}
