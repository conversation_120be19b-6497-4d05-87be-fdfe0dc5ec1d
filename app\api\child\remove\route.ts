import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import mockChildUserAPI from '@/mocks/endpoints/childUsers';

// Remove child user connection
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { childId } = await request.json();

    if (!childId) {
      return NextResponse.json(
        { error: 'Child ID is required' },
        { status: 400 }
      );
    }

    if (isDemoMode()) {
      // Use mock removal in demo mode
      const success = await mockChildUserAPI.removeChildUser(childId, user.id);
      
      return NextResponse.json({
        success,
        message: success ? 'Child user removed successfully (Demo Mode)' : 'Child user not found',
        demoMode: true
      });
    }

    // In production mode, handle real child user removal
    // This would typically:
    // 1. Verify the child belongs to this master
    // 2. Update master_child_relationships table to mark as inactive
    // 3. Optionally notify the child user
    // 4. Clean up any related data

    return NextResponse.json({
      success: true,
      message: 'Child user removed successfully',
      demoMode: false
    });

  } catch (error) {
    console.error('Error removing child user:', error);
    return NextResponse.json(
      { 
        error: 'Failed to remove child user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
