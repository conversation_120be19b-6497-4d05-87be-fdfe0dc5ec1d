import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import mockChildUserAPI from '@/mocks/endpoints/childUsers';

// Get list of connected child users
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (isDemoMode()) {
      // Use mock data in demo mode
      const childUsers = await mockChildUserAPI.getChildUsers(user.id);
      const pendingInvitations = await mockChildUserAPI.getPendingInvitations(user.id);
      
      return NextResponse.json({
        success: true,
        childUsers,
        pendingInvitations,
        demoMode: true
      });
    }

    // In production mode, query real database
    // This would typically query the master_child_relationships table
    // joined with users table to get child user details

    return NextResponse.json({
      success: true,
      childUsers: [],
      pendingInvitations: [],
      demoMode: false
    });

  } catch (error) {
    console.error('Error fetching child users:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch child users',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
