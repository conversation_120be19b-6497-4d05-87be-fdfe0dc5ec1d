// Mock Child User Data for Demo Mode
export interface MockChildUser {
  id: string;
  email: string;
  name: string;
  masterId: string;
  connectedAt: string;
  status: 'active' | 'pending' | 'inactive';
  zerodhaUserId?: string;
  invitationToken?: string;
  lastActiveAt?: string;
}

export interface MockInvitation {
  id: string;
  masterEmail: string;
  childEmail: string;
  masterId: string;
  token: string;
  status: 'pending' | 'accepted' | 'expired' | 'cancelled';
  sentAt: string;
  expiresAt: string;
  acceptedAt?: string;
}

// Mock data storage (simulated database)
let mockChildUsers: MockChildUser[] = [
  {
    id: 'child_1',
    email: '<EMAIL>',
    name: 'Demo Child 1',
    masterId: 'master_1',
    connectedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
    status: 'active',
    zerodhaUserId: 'DEMO_CHILD1_123',
    lastActiveAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
  },
  {
    id: 'child_2',
    email: '<EMAIL>',
    name: 'Demo Child 2',
    masterId: 'master_1',
    connectedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    status: 'active',
    zerodhaUserId: 'DEMO_CHILD2_123',
    lastActiveAt: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30 minutes ago
  }
];

let mockInvitations: MockInvitation[] = [];

// Generate mock child user
export const generateMockChildUser = (
  email: string,
  masterId: string,
  name?: string
): MockChildUser => {
  return {
    id: `child_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    email,
    name: name || email.split('@')[0].replace('.', ' ').toUpperCase(),
    masterId,
    connectedAt: new Date().toISOString(),
    status: 'pending',
    lastActiveAt: new Date().toISOString()
  };
};

// Generate mock invitation
export const generateMockInvitation = (
  masterEmail: string,
  childEmail: string,
  masterId: string
): MockInvitation => {
  const token = `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 days

  return {
    id: `invitation_${Date.now()}`,
    masterEmail,
    childEmail,
    masterId,
    token,
    status: 'pending',
    sentAt: new Date().toISOString(),
    expiresAt
  };
};

// Mock API functions
export const mockChildUserAPI = {
  // Get child users for a master
  getChildUsers: async (masterId: string): Promise<MockChildUser[]> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockChildUsers.filter(child => child.masterId === masterId);
  },

  // Send invitation to child user
  inviteChildUser: async (
    masterEmail: string,
    childEmail: string,
    masterId: string
  ): Promise<MockInvitation> => {
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));

    // Check if child is already connected
    const existingChild = mockChildUsers.find(
      child => child.email === childEmail && child.masterId === masterId
    );
    if (existingChild) {
      throw new Error('Child user is already connected');
    }

    // Check if invitation already exists
    const existingInvitation = mockInvitations.find(
      inv => inv.childEmail === childEmail && inv.masterId === masterId && inv.status === 'pending'
    );
    if (existingInvitation) {
      throw new Error('Invitation already sent to this email');
    }

    // Create new invitation
    const invitation = generateMockInvitation(masterEmail, childEmail, masterId);
    mockInvitations.push(invitation);

    // Create pending child user
    const childUser = generateMockChildUser(childEmail, masterId);
    mockChildUsers.push(childUser);

    return invitation;
  },

  // Accept invitation and connect child user
  acceptInvitation: async (
    token: string,
    zerodhaUserId: string
  ): Promise<MockChildUser> => {
    await new Promise(resolve => setTimeout(resolve, 1000));

    const invitation = mockInvitations.find(inv => inv.token === token);
    if (!invitation) {
      throw new Error('Invalid invitation token');
    }

    if (invitation.status !== 'pending') {
      throw new Error('Invitation has already been processed');
    }

    if (new Date(invitation.expiresAt) < new Date()) {
      throw new Error('Invitation has expired');
    }

    // Update invitation status
    invitation.status = 'accepted';
    invitation.acceptedAt = new Date().toISOString();

    // Update child user
    const childUser = mockChildUsers.find(
      child => child.email === invitation.childEmail && child.masterId === invitation.masterId
    );
    if (childUser) {
      childUser.status = 'active';
      childUser.zerodhaUserId = zerodhaUserId;
      childUser.connectedAt = new Date().toISOString();
    }

    return childUser!;
  },

  // Remove child user
  removeChildUser: async (childId: string, masterId: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));

    const initialLength = mockChildUsers.length;
    mockChildUsers = mockChildUsers.filter(
      child => !(child.id === childId && child.masterId === masterId)
    );

    return mockChildUsers.length < initialLength;
  },

  // Get pending invitations for a master
  getPendingInvitations: async (masterId: string): Promise<MockInvitation[]> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockInvitations.filter(
      inv => inv.masterId === masterId && inv.status === 'pending'
    );
  },

  // Cancel invitation
  cancelInvitation: async (invitationId: string, masterId: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));

    const invitation = mockInvitations.find(
      inv => inv.id === invitationId && inv.masterId === masterId
    );
    if (invitation) {
      invitation.status = 'cancelled';
      return true;
    }

    return false;
  }
};

// Export for use in API routes
export default mockChildUserAPI;
