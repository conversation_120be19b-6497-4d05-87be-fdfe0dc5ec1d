import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import mockChildUserAPI from '@/mocks/endpoints/childUsers';

// Send invitation to child user
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { childEmail } = await request.json();

    if (!childEmail) {
      return NextResponse.json(
        { error: 'Child email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(childEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    if (isDemoMode()) {
      // Use mock invitation in demo mode
      const invitation = await mockChildUserAPI.inviteChildUser(
        user.email || '<EMAIL>',
        childEmail,
        user.id
      );

      return NextResponse.json({
        success: true,
        invitation,
        message: `Invitation sent to ${childEmail} (Demo Mode)`,
        demoMode: true
      });
    }

    // In production mode, handle real invitation
    // This would typically:
    // 1. Create invitation record in database
    // 2. Generate invitation token
    // 3. Send email with invitation link
    // 4. Return success response

    return NextResponse.json({
      success: true,
      message: `Invitation sent to ${childEmail}`,
      demoMode: false
    });

  } catch (error) {
    console.error('Error sending invitation:', error);
    
    // Handle specific error messages from mock API
    if (error instanceof Error) {
      if (error.message.includes('already connected') || error.message.includes('already sent')) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { 
        error: 'Failed to send invitation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
