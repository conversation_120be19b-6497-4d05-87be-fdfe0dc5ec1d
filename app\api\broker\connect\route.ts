import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import mockBrokerAPI from '@/mocks/endpoints/brokers';

// Handle broker connection
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { brokerId, authCode } = await request.json();

    if (!brokerId) {
      return NextResponse.json(
        { error: 'Broker ID is required' },
        { status: 400 }
      );
    }

    if (isDemoMode()) {
      // Use mock connection in demo mode
      const connection = await mockBrokerAPI.connectBroker(brokerId, user.id);
      return NextResponse.json({
        success: true,
        connection,
        message: `Successfully connected to ${connection.brokerName} (Demo Mode)`,
        demoMode: true
      });
    }

    // In production mode, handle real broker connection
    if (brokerId === 'kite') {
      if (!authCode) {
        // Return OAuth URL for Zerodha
        const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;
        if (!apiKey) {
          return NextResponse.json(
            { error: 'Zerodha API key not configured' },
            { status: 500 }
          );
        }

        const authUrl = `https://kite.zerodha.com/connect/login?v=3&api_key=${apiKey}`;
        return NextResponse.json({
          success: false,
          authUrl,
          message: 'Redirect to Zerodha for authentication'
        });
      }

      // Handle OAuth callback with auth code
      // This would exchange the auth code for access token
      // Implementation depends on Zerodha API documentation
      return NextResponse.json(
        { error: 'OAuth callback handling not implemented yet' },
        { status: 501 }
      );
    }

    return NextResponse.json(
      { error: `Broker ${brokerId} not supported yet` },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error connecting to broker:', error);
    return NextResponse.json(
      { 
        error: 'Failed to connect to broker',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
