import { NextRequest, NextResponse } from 'next/server';
import { isDemoMode, simulateApiDelay, logDemoAction, wrapWithDemoIndicator } from '@/lib/utils/demoMode';
import { mockPortfolioData } from '@/mocks/endpoints/portfolio';
import { createClient } from '@/utils/supabase/server';
import { PortfolioService } from '@/lib/services/portfolioService';

/**
 * GET /api/portfolio
 * Fetch portfolio data - returns mock data in demo mode, real data otherwise
 */
export async function GET(request: NextRequest) {
  try {
    // Simulate API delay in demo mode
    await simulateApiDelay();

    // Check if demo mode is enabled
    if (isDemoMode()) {
      logDemoAction('Fetching portfolio data from mock');

      // Return mock portfolio data
      return NextResponse.json({
        success: true,
        data: wrapWithDemoIndicator(mockPortfolioData),
        message: 'Portfolio data fetched successfully (Demo Mode)'
      });
    }

    // Production mode - fetch real data
    logDemoAction('Fetching portfolio data from real API');

    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: 'Please log in to access portfolio data'
        },
        { status: 401 }
      );
    }

    try {
      // Fetch portfolio data from database
      const portfolioSummary = await PortfolioService.getPortfolioSummary(user.id);

      // Transform data to match expected format
      const portfolioData = {
        totalValue: portfolioSummary.totalValue,
        totalPnL: portfolioSummary.totalPnL,
        totalPnLPercent: portfolioSummary.totalDayChangePercent,
        totalDayChange: portfolioSummary.totalDayChange,
        totalDayChangePercent: portfolioSummary.totalDayChangePercent,
        totalInvestedValue: portfolioSummary.totalValue - portfolioSummary.totalPnL,
        holdings: portfolioSummary.holdings.map(holding => ({
          symbol: holding.symbol,
          exchange: holding.exchange,
          quantity: Number(holding.quantity),
          averagePrice: Number(holding.averagePrice),
          currentPrice: Number(holding.currentPrice),
          pnl: Number(holding.currentPrice) * Number(holding.quantity) - Number(holding.averagePrice) * Number(holding.quantity),
          pnlPercent: ((Number(holding.currentPrice) - Number(holding.averagePrice)) / Number(holding.averagePrice)) * 100,
          dayChange: Number(holding.dayChange || 0),
          dayChangePercent: Number(holding.dayChangePercent || 0),
          marketValue: Number(holding.currentPrice) * Number(holding.quantity),
          investedValue: Number(holding.averagePrice) * Number(holding.quantity)
        })),
        lastUpdated: new Date().toISOString()
      };

      return NextResponse.json({
        success: true,
        data: portfolioData,
        message: 'Portfolio data fetched successfully'
      });

    } catch (dbError) {
      console.error('Database error:', dbError);

      // Fallback to mock data if database fails
      logDemoAction('Database error, falling back to mock data', dbError);

      return NextResponse.json({
        success: true,
        data: wrapWithDemoIndicator(mockPortfolioData),
        message: 'Portfolio data fetched successfully (Fallback Mode)',
        warning: 'Using demo data due to database connectivity issues'
      });
    }

  } catch (error) {
    console.error('Portfolio API error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch portfolio data',
        message: 'An error occurred while fetching your portfolio. Please try again.',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/portfolio
 * Update portfolio data (for future use)
 */
export async function POST(request: NextRequest) {
  try {
    if (isDemoMode()) {
      return NextResponse.json({
        success: false,
        error: 'Portfolio updates not available in demo mode',
        message: 'Portfolio updates are disabled in demo mode'
      }, { status: 403 });
    }

    // TODO: Implement portfolio update logic for production
    return NextResponse.json({
      success: false,
      error: 'Not implemented',
      message: 'Portfolio updates not yet implemented'
    }, { status: 501 });

  } catch (error) {
    console.error('Portfolio update error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update portfolio',
        message: 'An error occurred while updating your portfolio. Please try again.'
      },
      { status: 500 }
    );
  }
}
